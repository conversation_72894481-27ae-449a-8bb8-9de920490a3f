/**
 * 运营统计卡片组件
 * 
 * 显示总主播数、活跃主播数、总用户数等关键指标
 */

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui'
import { UsersRound, TrendingUp, DollarSign, Activity } from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatNumber, formatCurrency } from '@/utils'

export interface OperationsStats {
  totalUsers: number
  newUsersThisMonth?: number
  totalRechargeThisMonth?: number
  totalConsumeThisMonth?: number
  todayRecharge?: number
  statsTime?: string
}

export interface StatsCardsProps {
  stats: OperationsStats | null
  loading?: boolean
  error?: string | null
  className?: string
}



/**
 * 统计卡片项组件
 */
const StatsCard: React.FC<{
  title: string
  value: string | number
  description: string
  icon: React.ReactNode
  trend?: {
    value: number
    label: string
  }
  loading?: boolean
  className?: string
}> = ({ title, value, description, icon, trend, loading, className }) => {
  return (
    <Card className={cn('transition-all duration-200 hover:shadow-md', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <div className="text-muted-foreground">
          {icon}
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">
          {loading ? (
            <div className="h-8 w-16 bg-muted animate-pulse rounded" />
          ) : (
            value
          )}
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          {description}
        </p>
        {trend && !loading && (
          <div className="flex items-center mt-2 text-xs">
            <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
            <span className="text-green-500 font-medium">+{trend.value}</span>
            <span className="text-muted-foreground ml-1">{trend.label}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * 运营统计卡片组件
 */
export const StatsCards: React.FC<StatsCardsProps> = ({
  stats,
  loading = false,
  error,
  className
}) => {
  // 错误状态
  if (error) {
    return (
      <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
        <Card className="col-span-full">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">加载统计数据失败</p>
              <p className="text-xs text-muted-foreground mt-1">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={cn('grid gap-4 md:grid-cols-2 lg:grid-cols-3', className)}>
      {/* 总用户数 */}
      {/*<StatsCard
        title="总用户数"
        value={loading ? '' : formatNumber(stats?.totalUsers || 0)}
        description="所有用户总数"
        icon={<UsersRound className="h-4 w-4" />}
        trend={stats?.newUsersThisMonth ? {
          value: stats.newUsersThisMonth,
          label: '本月新增'
        } : undefined}
        loading={loading}
        className="border-blue-200 bg-blue-50/50"
      />*/}
      {/*利润金额*/}
      <StatsCard
        title="利润金额"
        value={loading ? '' : formatNumber(stats?.totalRechargeThisMonth * 0.10 || 0)}
        description="利润金额"
        icon={<UsersRound className="h-4 w-4" />}
        /*trend={stats?.newUsersThisMonth ? {
          value: stats.newUsersThisMonth,
          label: '同比'
        } : undefined}*/
        loading={loading}
        className="border-blue-200 bg-blue-50/50"
      />
      {/* 时间区间充值金额 */}
      {stats?.totalRechargeThisMonth !== undefined && (
        <StatsCard
          title="时间区间充值金额"
          value={loading ? '' : formatCurrency(stats.totalRechargeThisMonth)}
          description="选定时间范围内的充值金额"
          icon={<DollarSign className="h-4 w-4" />}
          loading={loading}
          className="border-green-200 bg-green-50/50"
        />
      )}

      {/* 今日充值金额 */}
      <StatsCard
        title="今日充值"
        value={loading ? '' : formatCurrency(stats?.todayRecharge || 0)}
        description="今天的充值总额"
        icon={<TrendingUp className="h-4 w-4" />}
        loading={loading}
        className="border-blue-200 bg-blue-50/50"
      />

      {/* 本月消费金额 */}
      {/*{stats?.totalConsumeThisMonth !== undefined && (*/}
      {/*  <StatsCard*/}
      {/*    title="本月消费"*/}
      {/*    value={loading ? '' : `¥${formatAmount(stats.totalConsumeThisMonth)}`}*/}
      {/*    description="本月总消费金额"*/}
      {/*    icon={<Activity className="h-4 w-4" />}*/}
      {/*    loading={loading}*/}
      {/*    className="border-purple-200 bg-purple-50/50"*/}
      {/*  />*/}
      {/*)}*/}
    </div>
  )
}

export default StatsCards
