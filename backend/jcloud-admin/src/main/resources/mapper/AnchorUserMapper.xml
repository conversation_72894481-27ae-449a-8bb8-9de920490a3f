<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.admin.mapper.AnchorUserMapper">

    <!-- 主播用户列表结果映射 -->
    <resultMap id="AnchorListResultMap" type="com.jcloud.admin.dto.response.AnchorListResponse">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="avatar" property="userimage" jdbcType="VARCHAR"/>
        <result column="status" property="state" jdbcType="INTEGER"/>
        <result column="identity" property="identity" jdbcType="INTEGER"/>
        <result column="isauth" property="isauth" jdbcType="INTEGER"/>
        <result column="coin" property="coin" jdbcType="DECIMAL"/>
        <result column="key_amount" property="key" jdbcType="DECIMAL"/>
        <result column="create_time" property="create_time" jdbcType="BIGINT"/>
        <result column="last_login_time" property="last_login_time" jdbcType="BIGINT"/>
        <result column="last_login_ip" property="last_login_ip" jdbcType="VARCHAR"/>
        <result column="invite_code" property="invite_code" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="INTEGER"/>
        <result column="exp" property="exp" jdbcType="DECIMAL"/>
        <result column="sub_user_count" property="sub_user_count" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 查询主播用户列表（手动分页） -->
    <select id="selectAnchorUserList" resultMap="AnchorListResultMap">
        SELECT
            u.id,
            u.nickname,
            u.username,
            u.phone,
            u.avatar,
            u.status,
            -- 从备注中提取identity信息
            CASE 
                WHEN u.remark LIKE '%identity=2%' THEN 2
                WHEN u.remark LIKE '%identity=3%' THEN 3
                ELSE 2
            END as identity,
            -- 默认认证状态（同步用户默认为已认证）
            1 as isauth,
            -- 业务数据字段（从vim_user同步时这些字段可能为空）
            0 as coin,
            0 as key_amount,
            u.create_time,
            u.last_login_time,
            u.last_login_ip,
            '' as invite_code,
            1 as level,
            0 as exp,
            0 as sub_user_count
        FROM sys_user u
        WHERE u.remark LIKE '%从vim_user同步的主播用户%'
        AND u.deleted = 0
        AND u.status = 1
        <if test="query.nickname != null and query.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
        </if>
        <if test="query.status != null">
            AND u.status = #{query.status}
        </if>
        <if test="query.identity != null">
            AND u.remark LIKE CONCAT('%identity=', #{query.identity}, '%')
        </if>
        <if test="query.registerStartTime != null">
            AND u.create_time &gt;= #{query.registerStartTime}
        </if>
        <if test="query.registerEndTime != null">
            AND u.create_time &lt;= #{query.registerEndTime}
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND u.phone LIKE CONCAT('%', #{query.phone}, '%')
        </if>
        ORDER BY u.create_time DESC
        <if test="query.pageSize != null and query.offset != null">
            LIMIT #{query.offset}, #{query.pageSize}
        </if>
    </select>

    <!-- 统计主播用户列表总数 -->
    <select id="countAnchorUserList" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_user u
        WHERE u.remark LIKE '%从vim_user同步的主播用户%'
        AND u.deleted = 0
        AND u.status = 1
        <if test="query.nickname != null and query.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
        </if>
        <if test="query.status != null">
            AND u.status = #{query.status}
        </if>
        <if test="query.identity != null">
            AND u.remark LIKE CONCAT('%identity=', #{query.identity}, '%')
        </if>
        <if test="query.registerStartTime != null">
            AND u.create_time &gt;= #{query.registerStartTime}
        </if>
        <if test="query.registerEndTime != null">
            AND u.create_time &lt;= #{query.registerEndTime}
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND u.phone LIKE CONCAT('%', #{query.phone}, '%')
        </if>
    </select>

</mapper>
