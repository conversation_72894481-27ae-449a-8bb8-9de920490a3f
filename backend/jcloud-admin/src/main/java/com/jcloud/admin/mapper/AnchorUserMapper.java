package com.jcloud.admin.mapper;

import com.jcloud.admin.dto.request.AnchorQueryRequest;
import com.jcloud.admin.dto.response.AnchorListResponse;
import com.jcloud.common.entity.SysUser;
import com.mybatisflex.core.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 主播用户Mapper接口
 * 查询sys_user表中的主播用户，自动应用数据权限过滤
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AnchorUserMapper extends BaseMapper<SysUser> {

    /**
     * 查询主播用户列表（手动分页）
     * 支持按昵称、状态等条件筛选，自动应用数据权限过滤
     *
     * @param query 查询条件（包含分页参数）
     * @return 主播用户列表
     */
    List<AnchorListResponse> selectAnchorUserList(@Param("query") AnchorQueryRequest query);

    /**
     * 统计主播用户列表总数
     * 支持按昵称、状态等条件筛选，自动应用数据权限过滤
     *
     * @param query 查询条件
     * @return 总记录数
     */
    Long countAnchorUserList(@Param("query") AnchorQueryRequest query);
}
